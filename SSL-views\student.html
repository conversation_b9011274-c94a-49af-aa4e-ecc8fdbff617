
<!DOCTYPE html>
<html>
<head>
<title>NKN Wifi</title>
    <link rel="shortcut icon" href="FAVICON">
    <link rel="stylesheet" type="text/css" href="NAVIGATION">
</head>
<body>
    <!-- Navigation bar for navstyle2.css -->
<div class="topnav">
  <a class="active" href="HOME">Home</a>
  <!-- <a href="#news">News</a> -->
  <!-- <a href="#contact">Contact</a>
  <a href="#about">About</a> -->
  <!-- <a style="float:right" href="queryFiles/logout.php">Logout</a> -->
</div>
 <!-- Navigation bar for navstyle2.css ends here -->
           
        <div class=center-image>
            <img src="LOGO" alt="Varsity Logo">  
            <h2 align="center">NKN-Internet & Wifi Access Registration Form</h2>
          </div>
    

<div class="container">
  <form name="form1" action="STUD-DATA" method="POST" enctype="multipart/form-data">
<h4>Student Registration</h4>

<label width=20px for="">Campus</label>  

<input class="radio" width=20px type="radio" id="AUS" name="campus" value="AUS" checked>
<label style="width: 100px;" class="radio" for="AUS">AUS</label>

<input class="radio" type="radio" id="AUDC" name="campus" value="AUDC">
<label class="radio" for="AUDC">AUDC</label>
<br><br>

<label width=20px for="">University ID Card No.</label>          
<input type="text" name="enroll" required>
<br><br>
        <label width=20px for="">First Name</label>          
        <input type="text" name="fname" onkeypress="return checkSpcialChar(event)" required>
        <br><br>
        <label for="">Last Name</label>          
        <input type="text" name="lname" onkeypress="return checkSpcialChar(event)" required>
        <br><br>
        <label for="">Father's Name</label>          
        <input type="text" name="father" onkeypress="return checkSpcialChar(event)" required>
        <br><br>
<!-- ____________________________________ Gender Start _____________________________________________________ -->
        <label width=20px for="">Gender</label>  
        <input class="radio" width=20px type="radio" id="Male" name="gender" value="Male" checked>
        <label style="width: 100px;" class="radio" for="Male">Male</label>
        <input class="radio" type="radio" id="Female" name="gender" value="Female">
        <label class="radio" for="Female">Female</label>
        <br><br>
<!-- ____________________________________ Gender End _____________________________________________________ -->
        <label for="">Date of Birth</label>          
        <input type="date" name="dob" required>
        <br><br>
        <label for="">Address</label>          
        <textarea rows="4" cols="26" name="addr"
        placeholder='House No, Street Name,        District                State                     Pin' required></textarea>
        <br><br>
        <label for="">Department/Centre/Section:</label>          
        <input type="text" name="dept" id="dept" required>  
        <br><br> 
        <label for="">Email Id:</label>                
        <input type="email" id="email" name="email" pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$" placeholder="Active ID to be given" required> 
        <!-- <label class="redText" for="">Active ID to be given</label> -->
        <br><br>  
        <label for="">Mobile Number</label>                

        <!-- <input name="mobno" type = "tel" placeholder="10 digit Number only" 
        pattern="[6-9]{1}[0-9]{3}[0-9]{3}[0-9]{3}" maxlength = "10" required/>     -->
        <input id="mobno" name="mobno" type="text" placeholder="Active No." onkeypress="return isNumber(event)" onchange="ValidateNo()" required/>
        <!-- <label class="redText" for="">Active No.</label> -->
        <br><br>
        <label for="">Course Name</label>                  
        <input type="text" name="course" required> 
        <br><br>
        <label for="">Semester/Year</label>                  
        <input  type="number" name="semester" pattern="/^-?\d+\.?\d*$/" onKeyPress="if(this.value.length==1) return false;"  required/> 
        <br><br>
        <label for="">Expected Date/Year of completion:</label>                  
        <input type="date" name="yearOfCompletion" required> 
	<label class="redText" for="">Valid upto date as on the ID Card</label>
 
        <br><br>
        <label for="">Hostel No</label>                  
        <input type="number" name="hostelNo" placeholder="For Hostellers only" pattern="/^-?\d+\.?\d*$/" onKeyPress="if(this.value.length==2) return false;" 
                value = "<?php echo  $_POST['hostelNo'] ? $_POST['hostelNo'] : 00 ?>"/> 
        <!-- <label class="redText" for="">For Hostellers only</label> -->
        <br>
        <p class="redText">Note: Only jpg, jpeg, png format is allowed, Only University ID Card will be Accepted </p>
        <br>
        <label for="idfront">University ID Front:</label>
        <input type='file' id="idfront" name='idf' required> 
        <label class="redText" for="">File size should not exceed 50kb</label>
        <br><br>
        <label for="idpb">University ID Back:</label>
        <input type='file' id="idpb" name='idb' required> 
        <label class="redText" for="">File size should not exceed 50kb</label>
        <br><br>
        <label for="dp">Passport Size Photo:</label>
        <input type='file' id="dp" name="pp" required> 
        <label class="redText" for="">File size should not exceed 50kb</label>
        
        <br><br>
        <HR> 
<H4>Terms and Conditions</H4>
<ul>
  <li>For authentication the User ID & Password will be given only through the given Email address.</li>
  <li>It is mandatory to change the password after first LOGIN.</li>
  <li>Computer Centre shall not share any user information with anyone unless authorized</li>
  <li>The User shall remain solely Responsible and Accountable for any type of misuse of Interner from his/her account. Any kind of misuse will lead the account to be deactivated whenever needed.</li>
  <li>Any kind of Misuse may lead to Legal consequences as per IT ACT 2000 and 2008</li>
  <li>All actions on internet are punishable in the same manner as if done in the physical space.</li>
</ul>

<H4>Undertaking</H4>
<ul>
  <li>I undertake that I would keep my password secret and I also understand that it is my responsibility to maintain its secrecy and I assume full responsibility for the same from the moment the password is given to me.</li>
  <li>I also understand taht if an unauthorized person acceses the internet on my password, I will be called to question and would have to own responsibility for the same. I have put my signature onto this applicatioin form to acknowledge this accountability/responsibility.</li>
</ul>

<br>        
        
 <div  class="checkbox">
            <input style="width: fit-content;" type="checkbox" value="iAgree" id="iAgree" name="iAgree" required>I declare that I have read and understoood the instructiions and also undertake to abide by all the above rules and regulations.
 </div>

 <br><br>

        <!-- <label> </label> -->
        <button class="btn-primary" type="submit" name="submit" value="Save">  Register </button>

  </form>
</div>

<div class="footer">
  <p>Copyright &copy Assam University, Silchar, Cachar, Assam</p>
</div>

</body>

<script text="text/javascript" src="js/functions.js"></script>

</html>
