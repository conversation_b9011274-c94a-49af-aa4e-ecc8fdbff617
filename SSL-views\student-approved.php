<?php

// turn on error reporting
// error_reporting(1);
// ini_set('error_reporting', E_ALL);

// start session
session_start();

if ($_SESSION["uid"] == null) {

  header("location:../HOME");
}

// debug session
// var_dump($_SESSION);
// echo '<br>';
// echo $_SESSION["meter_no"];

//The below codes just prints the session values
// echo '<br>';
// print_r($_SESSION);
// echo '<br>';
// echo("{$_SESSION['meter_no']}"."<br />");


$searchKeyword = isset($_GET['search']) ? $_GET['search'] : '';

if (isset($_POST['from'])) {
  $newFromDate = $_POST['from'];
} else if (isset($_GET['from'])) {
  $newFromDate = $_GET['from'];
}

if (isset($_POST['to'])) {
  $newToDate = $_POST['to'];
} else if (isset($_GET['to'])) {
  $newToDate = $_GET['to'];
}

// Get the selected year value
$selectedYear = '';
if (isset($_POST['year']) && !empty($_POST['year'])) {
  $selectedYear = $_POST['year'];
}

?>

<html>
<title>Approved Applicants</title>
<div class="topnav">
  <a href="../AHOME">Home</a>
  <a class="active" href="student-approved.php">Approved</a>
  <a href="student-rejected.php">Rejected</a>
  <a href="student-pending.php">Pending</a>
  <a style="float:right" href="../SIGNOUT">Logout</a>
</div>

<head>

  <link rel="stylesheet" type="text/css" href="../css/navbar.css">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" type="text/css" href="../css/modal.css">

</head>

<body> <!--to align the entire content in centre-->

  <div class="center-image">
    <img src="../resources/logo.png" alt="Varsity Logo">
    <h2 align="center">Approved Student Applicants List</h2>
  </div>

  <div class="search-form col">
    <form method="GET" action="<?php echo $_SERVER['PHP_SELF']; ?>">
      <label class="form-label" for="search">Search students:</label>
      <input class="form-control col-lg-3 mb-3" type="text" id="search" name="search" placeholder="enroll no., email, phone" value="<?php echo htmlspecialchars($searchKeyword, ENT_QUOTES); ?>">
      <input class="btn btn-primary " type="submit" value="Search">
      <input class="btn btn-secondary " type="button" value="Reset" onclick="location.replace(location.pathname);">
    </form>
  </div>
  <!-- <div align="right" style="margin-right: 10px">
      <a href="printBillList.php">Print Pdf</a>
    </div> -->
  <div id="block_container">
    <div id="bloc1">
      <?php
      include("../config/dbConfig.php");

      include("../SSL-views/pagination.php");
      ?>
    </div>
  </div>


  <div id="block_container" class="row">
    <div id="bloc1" class="col-lg-6">
      <form method="POST">
        <label class='less-space'>From: </label><input class='less-space' type="date" name="from" value="<?php echo $newFromDate; ?>">
        <label class='less-space'>To: </label><input class='less-space' type="date" name="to" value="<?php echo $newToDate; ?>">
        <input class='less-space' style='margin-right: 20px; width: 110px;' type="submit" value="Get Data" name="submit">
      </form>
    </div>



    <div id="bloc2" class="col-lg-6">
      <!-- ////////////////////////////// Export Part Starts /////////////////////// -->

      <form class="" action="functions.php" method="post" name="upload_excel" enctype="multipart/form-data">
        <div class="form-group">
          <div class="">
            <label class='less-space'>From: </label><input class='less-space' type="date" name="export-from">
            <label class='less-space'>To: </label><input class='less-space' type="date" name="export-to">
            <input class='less-space' type="submit" name="Export-Approved-Students" class="btn btn-success" value="Export to Excel" />
          </div>
        </div>
      </form>
      <!-- ////////////////////////////// Export Part Ends /////////////////////// -->
    </div>
  </div>

  <div class="block_container" class="row">
    <div id="block1" class="col-lg-6">
      <form method="POST">
        <label for="year">Completion Year</label>
        <select name="year" id="year">
          <option value="">Select Year</option>
          <!-- <option value=""></option> -->
        </select>
        <input class='less-space' style='margin-left: 10px; width: 110px;' type="submit" value="Filter by Year" name="filter_year">
      </form>
    </div>
  </div>

  <script>
    const syear = 2015;
    const eyear = new Date().getFullYear();
    const selectedYear = "<?php echo $selectedYear; ?>";
    console.log(selectedYear);

    const year = document.getElementById('year');
    for (let i = syear; i <= eyear; i++) {
      const option = document.createElement('option');
      option.value = i;
      option.text = i;
      if (i.toString() === selectedYear) {
        option.selected = true;
      }
      year.add(option);
    }
  </script>


  <div class='applicants-table'>

    <table>
      <tr>
        <th>Sl No</th>
        <th>Campus</th>
        <th>University ID No</th>
        <th>Name</th>
        <th>Department</th>
        <th>Course</th>
        <th>Sem</th>
        <th>Completion Year</th>
        <th>Father</th>
        <th>Gender</th>
        <th>Email</th>
        <th>Phone</th>
        <th>ID Front</th>
        <th>ID Back</th>
        <th>Photo</th>

      </tr>
      <?php

      /* To connect to db */
      // include("../config/dbConfig.php");

      if (isset($_POST['from']) && isset($_POST['to'])) {
        $from = date('Y-m-d', strtotime($_POST['from']));
        $to = date('Y-m-d', strtotime($_POST['to']));
      } else if (isset($_GET['from']) && isset($_GET['to'])) {
        $from = date('Y-m-d', strtotime($_GET['from']));
        $to = date('Y-m-d', strtotime($_GET['to']));
      } else {
        $from = "";
        $to = "";
      }
      //define total number of results you want per page
      $results_per_page = 10;

      if ($from != "" && $to != "") {
        //find the total number of results stored in the database
        $query = "SELECT *, @ab:=@ab+1 AS SrNo FROM student_user, (SELECT @ab:= 0)
                 AS ab  JOIN data_uploads
                 WHERE student_user.enrollNo = data_uploads.enrollNo  AND isApproved =1 AND approvedOn BETWEEN '$from' AND '$to'";

        // Add year filter if selected
        if (!empty($selectedYear)) {
          $query .= " AND YEAR(yearOfCompletion) = '$selectedYear'";
        }
      } else if (!empty($selectedYear)) {
        // Only filter by year
        $query = "SELECT *, @ab:=@ab+1 AS SrNo FROM student_user, (SELECT @ab:= 0)
                 AS ab  JOIN data_uploads
                 WHERE student_user.enrollNo = data_uploads.enrollNo  AND isApproved =1 AND YEAR(yearOfCompletion) = '$selectedYear'";
      } else {
        $query = "SELECT *, @ab:=@ab+1 AS SrNo FROM student_user, (SELECT @ab:= 0)
                 AS ab  JOIN data_uploads
                 WHERE student_user.enrollNo = data_uploads.enrollNo  AND isApproved =1";
      }
      $result = mysqli_query($conn, $query);
      //echo "Total query ".$query."<br>";

      $number_of_result = mysqli_num_rows($result);
      //echo "total ".$number_of_result."<br>";

      //determine the total number of pages available
      $number_of_page = ceil($number_of_result / $results_per_page);

      //determine which page number visitor is currently on
      if (!isset($_GET['page'])) {
        $page = 1;
      } else {
        $page = $_GET['page'];
      }

      //determine the sql LIMIT starting number for the results on the displaying page
      $page_first_result = ($page - 1) * $results_per_page;

      if (isset($_POST['submit']) || isset($_GET['submit']) || isset($_POST['filter_year'])) {

        // Handle date filter
        if (isset($_POST['from']) && isset($_POST['to'])) {
          $from = date('Y-m-d', strtotime($_POST['from']));
          $to = date('Y-m-d', strtotime($_POST['to']));
        } else if (isset($_GET['from']) && isset($_GET['to'])) {
          $from = date('Y-m-d', strtotime($_GET['from']));
          $to = date('Y-m-d', strtotime($_GET['to']));
        }

        // Base SQL query
        $sql = "SELECT *, @ab:=@ab+1 AS SrNo FROM student_user, (SELECT @ab:= 0)
          AS ab JOIN data_uploads WHERE
          student_user.enrollNo = data_uploads.enrollNo";

        // Add date filter if dates are provided
        if (!empty($from) && !empty($to)) {
          $sql .= " AND approvedOn BETWEEN '$from' AND '$to'";
        }

        // Add year filter if year is selected
        if (!empty($selectedYear)) {
          $sql .= " AND YEAR(yearOfCompletion) = '$selectedYear'";
        }

        // Add isApproved condition
        $sql .= " AND isApproved = 1";

        // $sql = "SELECT *, ROW_NUMBER() OVER (ORDER BY id DESC) AS SrNo FROM `student_user` JOIN data_uploads WHERE
        // student_user.enrollNo = data_uploads.enrollNo  AND isApproved =1";

        $result = $conn->query($sql);

        if ($result !== false && $result->num_rows > 0) {
          // output data of each row
          while ($row = $result->fetch_assoc()) {
            echo "<tr>
            <td>" . $row["SrNo"] . "</td>
            <td>" . $row["campus"] . "</td>
            <td>" . $row["enrollNo"] . "</td>
            <td>" . $row["firstName"] . ' ' . $row["lastName"] . "</td>
            <td>" . $row["department"] . "</td>
            <td>" . $row["courseName"] . "</td>
            <td>" . $row["semester"] . "</td>
            <td>" . $row["yearOfCompletion"] . "</td>
            <td>" . $row["fatherName"] . "</td>
            <td>" . $row["gender"] . "</td>
            <td>" . $row["email"] . "</td>
            <td>" . $row["mobNo"] . "</td>
            <td><a id='IdFront-" . $row["SrNo"] . "' href='javascript:void(0);' onClick='myFunc(this)' data-src='" . $row['IdFront'] . "'>View</a></td>
            <td><a id='IdBack-" . $row["SrNo"] . "' href='javascript:void(0);' onClick='myFunc(this)' data-src='" . $row['IdBack'] . "'>View</a></td>
            <td><a id='Photo-" . $row["SrNo"] . "' href='javascript:void(0);' onClick='myFunc(this)' data-src='" . $row['Photo'] . "'>View</a></td>
      </tr>";
          }
          echo "</table>";
        } else {
          echo "0 results";
        }
        $conn->close();
      } else {

        //  $sql = "SELECT *, (@cnt := IF(@cnt IS NULL, 0,  @cnt) + 1) AS SrNo FROM `student_user` JOIN data_uploads WHERE
        //         student_user.enrollNo = data_uploads.enrollNo  AND isApproved =1 ORDER BY id DESC"; // Commented on 20-11-2021

        $sql = "SELECT *, @ab:=@ab+1 AS SrNo FROM student_user, (SELECT @ab:= 0)
                 AS ab  JOIN data_uploads
                 WHERE student_user.enrollNo = data_uploads.enrollNo  AND isApproved =1";

        // Add year filter if year is selected
        if (!empty($selectedYear)) {
          $sql .= " AND YEAR(yearOfCompletion) = '$selectedYear'";
        }

        if (!empty($searchKeyword)) {
          $sql .= " AND (
                    firstName LIKE '%$searchKeyword%'
                    OR lastName LIKE '%$searchKeyword%'
                    OR email LIKE '%$searchKeyword%'
                    OR mobNo LIKE '%$searchKeyword%'
                )";
        }

        $sql .= " order by SrNo DESC LIMIT " . $offset . ',' . $results_per_page;

        $result = $conn->query($sql);

        if ($result !== false && $result->num_rows > 0) {
          // output data of each row
          while ($row = $result->fetch_assoc()) {
            echo "<tr>
            <td>" . $row["SrNo"] . "</td>
            <td>" . $row["campus"] . "</td>
            <td>" . $row["enrollNo"] . "</td>
            <td>" . $row["firstName"] . ' ' . $row["lastName"] . "</td>
            <td>" . $row["department"] . "</td>
            <td>" . $row["courseName"] . "</td>
            <td>" . $row["semester"] . "</td>
            <td>" . $row["yearOfCompletion"] . "</td>
            <td>" . $row["fatherName"] . "</td>
            <td>" . $row["gender"] . "</td>
            <td>" . $row["email"] . "</td>
            <td>" . $row["mobNo"] . "</td>
            <td><a id='IdFront-" . $row["SrNo"] . "' href='javascript:void(0);' onClick='myFunc(this)' data-src='" . $row['IdFront'] . "'>View</a></td>
            <td><a id='IdBack-" . $row["SrNo"] . "' href='javascript:void(0);' onClick='myFunc(this)' data-src='" . $row['IdBack'] . "'>View</a></td>
            <td><a id='Photo-" . $row["SrNo"] . "' href='javascript:void(0);' onClick='myFunc(this)' data-src='" . $row['Photo'] . "'>View</a></td>
      </tr>";
          }
          echo "</table>";
        } else {
          echo "0 Approved";
        }
        $conn->close();
      }


      // $sql = "SELECT *, ROW_NUMBER() OVER (ORDER BY id DESC) AS SrNo FROM `student_user` JOIN data_uploads WHERE
      // student_user.enrollNo = data_uploads.enrollNo  AND isApproved =1";

      ?>
    </table>
    <div style='padding: 10px 20px 0px; border-top: dotted 1px #CCC; text-align: right;'>
      <strong>Page <?php echo $page_no . " of " . $total_no_of_pages; ?></strong>
    </div>
  </div>

  <?php

  //display the link of the pages in URL
  // for ($page = 1; $page <= $total_no_of_pages; $page++) {
  //   echo '<a href = "student-approved.php?page=' . $page . '">' . $page . ' </a>';
  // }
  ?>

  <!-- The Modal -->
  <div id="myModal" class="modal">
    <span class="close">&times;</span>
    <img class="modal-content" id="img01">
    <div id="caption"></div>
  </div>

</body>

</html>

</body>
<script text="text/javascript" src="../js/modal.js"></script>

</html>